import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';

import '../../Cattle/models/cattle_isar.dart';
import '../controllers/milk_details_controller.dart';
import '../dialogs/milk_form_dialog.dart';

import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_bar.dart';
import '../../../utils/message_utils.dart';

import 'milk_details_analytics_tab.dart';
import 'milk_details_records_tab.dart';

class MilkDetailsScreen extends StatefulWidget {
  final CattleIsar cattle;
  final Function(CattleIsar)? onCattleUpdated;

  const MilkDetailsScreen({
    Key? key,
    required this.cattle,
    this.onCattleUpdated,
  }) : super(key: key);

  @override
  State<MilkDetailsScreen> createState() => _MilkDetailsScreenState();
}

class _MilkDetailsScreenState extends State<MilkDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    if (kDebugMode) {
      debugPrint('DEBUG: MilkDetailsScreen.initState called');
      debugPrint('DEBUG: cattle.name = ${widget.cattle.name}');
      debugPrint('DEBUG: cattle.tagId = ${widget.cattle.tagId}');
    }

    // Analytics and Records tabs
    _tabController = TabController(
      length: 2, // Analytics, Records
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Show add milk record dialog
  void _showAddMilkDialog(MilkDetailsController controller) async {
    showDialog(
      context: context,
      builder: (context) => MilkFormDialog(
        cattle: [widget.cattle], // Pass as list
        onSave: (milkRecord) async {
          await controller.refresh();
          if (mounted) {
            MessageUtils.showSuccess(context, 'Milk record added successfully');
          }
        },
      ),
    );
  }

  /// Get current tab action based on selected tab
  void _getCurrentTabAction(MilkDetailsController controller) {
    switch (_tabController.index) {
      case 0:
        // Analytics tab - no FAB action
        break;
      case 1:
        _showAddMilkDialog(controller);
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<MilkDetailsController>(
      create: (_) => MilkDetailsController()..initialize(widget.cattle),
      child: Consumer<MilkDetailsController>(
        builder: (context, controller, child) {
          // Handle different states
          if (controller.isLoading) {
            return Scaffold(
              appBar: AppBarConfig.withBack(
                context: context,
                title: 'Loading...',
              ),
              body: const Center(
                child: CircularProgressIndicator(),
              ),
            );
          }

          if (controller.error != null) {
            return Scaffold(
              appBar: AppBarConfig.withBack(
                context: context,
                title: 'Error',
              ),
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red[300],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Error loading milk details',
                      style: Theme.of(context).textTheme.headlineSmall,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      controller.error!,
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: () => controller.refresh(),
                      icon: const Icon(Icons.refresh),
                      label: const Text('Retry'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          return Builder(
            builder: (context) {
              // Initialize tab manager here where Provider context is available
              _tabManager ??= UniversalTabManager.twoTabs(
                controller: _tabController,
                tabViews: const [
                  MilkDetailsAnalyticsTab(),
                  MilkDetailsRecordsTab(),
                ],
                labels: const ['Analytics', 'Records'],
                icons: const [Icons.analytics, Icons.list_alt],
                showFABs: const [false, true], // FAB only on records tab
              );

              return Scaffold(
                appBar: AppBarConfig.withBack(
                  context: context,
                  title: '${controller.cattle?.name ?? 'Milk'} (${controller.cattle?.tagId ?? 'No Tag'})',
                ),
                body: _tabManager!,
                floatingActionButton: AnimatedBuilder(
                  animation: _tabController,
                  builder: (context, child) {
                    // Only rebuild FAB when tab changes, not the entire screen
                    return _tabManager?.getCurrentFAB(
                      onPressed: () => _getCurrentTabAction(controller),
                      tooltip: 'Add Record',
                      backgroundColor: AppColors.milkHeader,
                    ) ?? const SizedBox.shrink();
                  },
                ),
              );
            },
          );
        },
      ),
    );
  }
}


