import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/milk_record_isar.dart';
import '../controllers/milk_details_controller.dart';
import '../dialogs/milk_form_dialog.dart';
import '../../widgets/universal_record_card.dart';
import '../../../constants/app_colors.dart';
import '../../../utils/message_utils.dart';

class MilkDetailsRecordsTab extends StatefulWidget {
  const MilkDetailsRecordsTab({Key? key}) : super(key: key);

  @override
  State<MilkDetailsRecordsTab> createState() => _MilkDetailsRecordsTabState();
}

class _MilkDetailsRecordsTabState extends State<MilkDetailsRecordsTab>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return Consumer<MilkDetailsController>(
      builder: (context, controller, child) {
        final cattle = controller.cattle;
        final milkRecords = controller.milkRecords;

        // Handle loading state
        if (controller.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        // Handle error state
        if (controller.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red[300],
                ),
                const SizedBox(height: 16),
                Text(
                  'Error loading milk records',
                  style: Theme.of(context).textTheme.headlineSmall,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  controller.error!,
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () => controller.refresh(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          );
        }

        // Handle empty state
        if (milkRecords.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.water_drop,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No Milk Records',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Milk production records for ${cattle?.name ?? 'this cattle'} will appear here.',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[500],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 80), // Bottom padding for FAB
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section header
              _buildSectionHeader(milkRecords.length),
              const SizedBox(height: 16),
              // Records list
              _buildRecordsList(milkRecords),
            ],
          ),
        );
      },
    );
  }

  /// Build section header with record count
  Widget _buildSectionHeader(int recordCount) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.milkHeader.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.milkHeader.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.water_drop,
            color: AppColors.milkHeader,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Milk Production Records',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.milkHeader,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '$recordCount ${recordCount == 1 ? 'record' : 'records'} found',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build records list
  Widget _buildRecordsList(List<MilkRecordIsar> milkRecords) {
    // Sort records by date (newest first)
    final sortedRecords = List<MilkRecordIsar>.from(milkRecords)
      ..sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));

    return Column(
      children: sortedRecords.map((record) => Container(
        margin: const EdgeInsets.only(bottom: 8),
        child: _buildMilkRecordCard(record),
      )).toList(),
    );
  }

  /// Build individual milk record card following weight module pattern
  Widget _buildMilkRecordCard(MilkRecordIsar record) {
    // Format date
    String dateText = _formatDate(record.date);

    // Format total production - use computed totalYield if totalAmount is null or zero
    double total = (record.totalAmount != null && record.totalAmount! > 0)
        ? record.totalAmount!
        : record.totalYield;
    String totalProduction = '${total.toStringAsFixed(1)}L';

    // Format session info
    String sessionInfo = _getSessionInfo(record);

    // Format cattle name with tag ID - removed unused variable

    return UniversalRecordCard(
      row1Left: dateText,
      row1Right: totalProduction,
      row1LeftIcon: Icons.calendar_today,
      row1RightIcon: Icons.water_drop,
      row2Left: sessionInfo,
      row2Right: '', // Empty right side
      row2LeftIcon: Icons.schedule,
      row2RightIcon: null, // No right icon
      notes: record.notes?.isNotEmpty == true ? record.notes : null,
      primaryColor: AppColors.milkHeader,
      onTap: () => _editMilkRecord(record),
      onEdit: () => _editMilkRecord(record),
      onDelete: () => _deleteMilkRecord(record),
    );
  }

  /// Get session information for display
  String _getSessionInfo(MilkRecordIsar record) {
    List<String> sessions = [];
    if ((record.morningAmount ?? 0) > 0) sessions.add('M: ${record.morningAmount?.toStringAsFixed(1)}L');
    if ((record.afternoonAmount ?? 0) > 0) sessions.add('A: ${record.afternoonAmount?.toStringAsFixed(1)}L');
    if ((record.eveningAmount ?? 0) > 0) sessions.add('E: ${record.eveningAmount?.toStringAsFixed(1)}L');

    return sessions.isNotEmpty ? sessions.join(', ') : 'No sessions';
  }

  /// Edit milk record
  void _editMilkRecord(MilkRecordIsar record) {
    final controller = context.read<MilkDetailsController>();
    final cattle = controller.cattle;

    if (cattle == null) return;

    showDialog(
      context: context,
      builder: (context) => MilkFormDialog(
        cattle: [cattle],
        // TODO: Add existingRecord parameter to MilkFormDialog
        onSave: (updatedRecord) async {
          await controller.refresh();
          if (mounted) {
            MessageUtils.showSuccess(context, 'Milk record updated successfully');
          }
        },
      ),
    );
  }

  /// Delete milk record
  void _deleteMilkRecord(MilkRecordIsar record) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Milk Record'),
        content: const Text('Are you sure you want to delete this milk record? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              // TODO: Implement delete functionality in controller
              // await controller.deleteMilkRecord(record);
              if (mounted) {
                MessageUtils.showSuccess(context, 'Milk record deleted successfully');
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// Format date for display
  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return DateFormat('MMM dd, yyyy').format(date);
  }
}
