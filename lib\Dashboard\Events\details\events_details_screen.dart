import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';

import '../../Cattle/models/cattle_isar.dart';
import '../controllers/events_details_controller.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_tabs.dart';

import 'events_details_analytics_tab.dart';
import 'events_details_records_tab.dart';

class EventsDetailsScreen extends StatefulWidget {
  final CattleIsar cattle;
  final Function(CattleIsar)? onCattleUpdated;

  const EventsDetailsScreen({
    Key? key,
    required this.cattle,
    this.onCattleUpdated,
  }) : super(key: key);

  @override
  State<EventsDetailsScreen> createState() => _EventsDetailsScreenState();
}

class _EventsDetailsScreenState extends State<EventsDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    
    // Initialize the controller with the cattle data
    final controller = GetIt.instance<EventsDetailsController>();
    controller.initialize(widget.cattle);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<EventsDetailsController>(
      create: (_) => GetIt.instance<EventsDetailsController>(),
      child: Scaffold(
        appBar: AppBar(
          title: Text('Events Details - ${widget.cattle.name ?? widget.cattle.tagId}'),
          backgroundColor: AppColors.eventsHeader,
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            final controller = Provider.of<EventsDetailsController>(context, listen: false);
            await controller.refresh();
          },
          child: Consumer<EventsDetailsController>(
            builder: (context, controller, child) {
              // Initialize tab manager here where Provider context is available
              _tabManager ??= UniversalTabManager.twoTabs(
                controller: _tabController,
                tabViews: const [
                  EventsDetailsAnalyticsTab(),
                  EventsDetailsRecordsTab(),
                ],
                labels: const ['Analytics', 'Records'],
                icons: const [Icons.analytics, Icons.list_alt],
                showFABs: const [false, true], // FAB for records tab
              );

              return _tabManager!.build(context);
            },
          ),
        ),
      ),
    );
  }
}
