import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';

import '../../Cattle/models/cattle_isar.dart';
import '../models/event_isar.dart';
import '../services/events_repository.dart';


/// Controller for managing events details screen state and data
class EventsDetailsController extends ChangeNotifier {
  final EventsRepository _eventsRepository = GetIt.instance<EventsRepository>();
  final Isar _isar = GetIt.instance<Isar>();

  // State management
  bool _isLoading = false;
  String? _error;
  CattleIsar? _cattle;

  // Events data
  List<EventIsar> _events = [];

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  CattleIsar? get cattle => _cattle;
  List<EventIsar> get events => _events;

  // Computed properties for analytics
  int get totalEvents => _events.length;
  
  int get upcomingEvents {
    final now = DateTime.now();
    return _events.where((event) {
      final eventDate = event.date;
      return eventDate != null && eventDate.isAfter(now);
    }).length;
  }
  
  int get completedEvents {
    return _events.where((event) => event.isCompleted == true).length;
  }
  
  int get overdueEvents {
    final now = DateTime.now();
    return _events.where((event) {
      final eventDate = event.date;
      return eventDate != null && 
             eventDate.isBefore(now) && 
             event.isCompleted != true;
    }).length;
  }
  
  List<EventIsar> get recentEvents {
    final sorted = List<EventIsar>.from(_events)
      ..sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
    return sorted.take(10).toList();
  }

  Map<EventType, int> get eventTypeDistribution {
    final distribution = <EventType, int>{};
    for (final event in _events) {
      final type = event.type?.toEventType() ?? EventType.miscellaneous;
      distribution[type] = (distribution[type] ?? 0) + 1;
    }
    return distribution;
  }

  Map<EventPriority, int> get priorityDistribution {
    final distribution = <EventPriority, int>{};
    for (final event in _events) {
      final priority = event.priority;
      distribution[priority] = (distribution[priority] ?? 0) + 1;
    }
    return distribution;
  }

  List<EventIsar> get highPriorityEvents {
    return _events.where((event) => event.priority == EventPriority.high).toList();
  }

  double get completionRate {
    if (_events.isEmpty) return 0.0;
    return (completedEvents / _events.length) * 100;
  }

  /// Initialize controller with cattle data
  void initialize(CattleIsar cattle) {
    _cattle = cattle;
    _loadEventsData();
  }

  /// Load all events data for the cattle
  Future<void> _loadEventsData() async {
    if (_cattle == null) return;

    try {
      _setLoading(true);
      _clearError();

      // Load all events and filter for this cattle
      final allEvents = await _isar.eventIsars.where().findAll();
      _events = allEvents.where((event) => event.cattleId == _cattle!.businessId).toList();

      _setLoading(false);
    } catch (e) {
      _setError('Failed to load events data: $e');
      _setLoading(false);
    }
  }

  /// Refresh all data
  Future<void> refresh() async {
    await _loadEventsData();
  }

  /// Add a new event
  Future<bool> addEvent(EventIsar event) async {
    try {
      _setLoading(true);
      await _eventsRepository.saveEvent(event);
      await _loadEventsData(); // Refresh data
      return true;
    } catch (e) {
      _setError('Failed to add event: $e');
      _setLoading(false);
      return false;
    }
  }

  /// Update an existing event
  Future<bool> updateEvent(EventIsar event) async {
    try {
      _setLoading(true);
      await _eventsRepository.saveEvent(event);
      await _loadEventsData(); // Refresh data
      return true;
    } catch (e) {
      _setError('Failed to update event: $e');
      _setLoading(false);
      return false;
    }
  }

  /// Delete an event
  Future<bool> deleteEvent(String eventId) async {
    try {
      _setLoading(true);
      // Find the event by business ID and delete by Isar ID
      final event = _events.firstWhere((e) => e.businessId == eventId);
      await _eventsRepository.deleteEvent(event.id);
      await _loadEventsData(); // Refresh data
      return true;
    } catch (e) {
      _setError('Failed to delete event: $e');
      _setLoading(false);
      return false;
    }
  }

  /// Mark event as completed
  Future<bool> markEventCompleted(String eventId) async {
    try {
      _setLoading(true);

      // Find the event and update it
      final event = _events.firstWhere((e) => e.businessId == eventId);
      event.isCompleted = true;
      await _eventsRepository.saveEvent(event);

      await _loadEventsData(); // Refresh data
      return true;
    } catch (e) {
      _setError('Failed to mark event as completed: $e');
      _setLoading(false);
      return false;
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  @override
  void dispose() {
    // Clean up any resources
    super.dispose();
  }
}
