import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import '../controllers/events_details_controller.dart';
import '../models/event_isar.dart';
import '../dialogs/event_form_dialog.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_tabs.dart';
import '../../../utils/message_utils.dart';



class EventsDetailsRecordsTab extends StatefulWidget {
  const EventsDetailsRecordsTab({super.key});

  @override
  State<EventsDetailsRecordsTab> createState() => _EventsDetailsRecordsTabState();
}

class _EventsDetailsRecordsTabState extends State<EventsDetailsRecordsTab>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return Consumer<EventsDetailsController>(
      builder: (context, controller, child) {
        if (controller.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (controller.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  controller.error!,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () => controller.refresh(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          );
        }

        final events = controller.events;

        if (events.isEmpty) {
          return UniversalTabEmptyState.forTab(
            title: 'No Events',
            message: 'No events recorded for ${controller.cattle?.name ?? 'this cattle'}.',
            tabColor: AppColors.eventsKpiColors[0],
            tabIndex: 0,

          );
        }

        return Scaffold(
          body: Column(
            children: [
              _buildControls(controller),
              Expanded(
                child: _buildEventsList(events),
              ),
            ],
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: () => _addEvent(context, controller),
            tooltip: 'Add Event',
            backgroundColor: AppColors.eventsHeader,
            child: const Icon(Icons.add),
          ),
        );
      },
    );
  }

  Widget _buildControls(EventsDetailsController controller) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.eventsHeader.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.event, color: AppColors.eventsHeader),
                  const SizedBox(width: 8),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Events',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.eventsHeader,
                        ),
                      ),
                      Text(
                        '${controller.events.length} events',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.refresh(),
            tooltip: 'Refresh',
          ),
        ],
      ),
    );
  }

  Widget _buildEventsList(List<EventIsar> events) {
    // Sort events by date (most recent first)
    final sortedEvents = List<EventIsar>.from(events)
      ..sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: sortedEvents.length,
      itemBuilder: (context, index) {
        final event = sortedEvents[index];
        return _buildEventCard(event);
      },
    );
  }

  Widget _buildEventCard(EventIsar event) {
    final isOverdue = event.date != null && 
                     event.date!.isBefore(DateTime.now()) && 
                     event.isCompleted != true;
    final isUpcoming = event.date != null && event.date!.isAfter(DateTime.now());
    
    Color statusColor;
    if (event.isCompleted == true) {
      statusColor = Colors.green;
    } else if (isOverdue) {
      statusColor = Colors.red;
    } else if (isUpcoming) {
      statusColor = Colors.blue;
    } else {
      statusColor = Colors.orange;
    }
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: () => _viewEventDetails(event),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getEventTypeIcon(event.type?.toEventType()),
                      color: statusColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          event.title ?? 'Untitled Event',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          event.date != null 
                              ? DateFormat('MMM dd, yyyy').format(event.date!)
                              : 'No date set',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: statusColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getStatusText(event, isOverdue, isUpcoming),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleMenuAction(value, event),
                    itemBuilder: (context) => [
                      if (event.isCompleted != true)
                        const PopupMenuItem(
                          value: 'complete',
                          child: Row(
                            children: [
                              Icon(Icons.check, size: 16),
                              SizedBox(width: 8),
                              Text('Mark Complete'),
                            ],
                          ),
                        ),
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 16),
                            SizedBox(width: 8),
                            Text('Edit'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 16, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              if (event.description?.isNotEmpty == true) ...[
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    event.description!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  IconData _getEventTypeIcon(EventType? type) {
    switch (type) {
      case EventType.vaccination:
        return Icons.vaccines;
      case EventType.healthCheckup:
        return Icons.medical_services;
      case EventType.breeding:
        return Icons.favorite;
      case EventType.pregnancyCheck:
        return Icons.pregnant_woman;
      case EventType.weightMeasurement:
        return Icons.monitor_weight;
      case EventType.deworming:
        return Icons.healing;
      case EventType.dryOff:
        return Icons.pause_circle;
      case EventType.calving:
        return Icons.child_care;
      case EventType.purchased:
        return Icons.shopping_cart;
      case EventType.sold:
        return Icons.sell;
      default:
        return Icons.event;
    }
  }

  String _getStatusText(EventIsar event, bool isOverdue, bool isUpcoming) {
    if (event.isCompleted == true) {
      return 'Completed';
    } else if (isOverdue) {
      return 'Overdue';
    } else if (isUpcoming) {
      return 'Upcoming';
    } else {
      return 'Pending';
    }
  }

  void _addEvent(BuildContext context, EventsDetailsController controller) {
    if (controller.cattle == null) return;

    showDialog(
      context: context,
      builder: (context) => EventFormDialog(
        cattleId: controller.cattle!.businessId!,
        onSave: (event) async {
          final success = await controller.addEvent(event);
          if (!mounted) return;
          if (success) {
            MessageUtils.showSuccess(context, 'Event added successfully');
          }
        },
      ),
    );
  }

  void _viewEventDetails(EventIsar event) {
    // TODO: Implement event details view
    MessageUtils.showInfo(context, 'Event details view not yet implemented');
  }

  void _handleMenuAction(String action, EventIsar event) {
    switch (action) {
      case 'complete':
        _markEventComplete(event);
        break;
      case 'edit':
        _editEvent(event);
        break;
      case 'delete':
        _deleteEvent(event);
        break;
    }
  }

  void _markEventComplete(EventIsar event) async {
    final controller = Provider.of<EventsDetailsController>(context, listen: false);
    final success = await controller.markEventCompleted(event.businessId!);
    if (success && mounted) {
      MessageUtils.showSuccess(context, 'Event marked as completed');
    }
  }

  void _editEvent(EventIsar event) {
    final controller = Provider.of<EventsDetailsController>(context, listen: false);
    if (controller.cattle == null) return;

    showDialog(
      context: context,
      builder: (context) => EventFormDialog(
        cattleId: controller.cattle!.businessId!,
        event: event,
        onSave: (updatedEvent) async {
          final success = await controller.updateEvent(updatedEvent);
          if (!mounted) return;
          if (success) {
            MessageUtils.showSuccess(context, 'Event updated successfully');
          }
        },
      ),
    );
  }

  void _deleteEvent(EventIsar event) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Event'),
        content: const Text('Are you sure you want to delete this event? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final controller = Provider.of<EventsDetailsController>(context, listen: false);
              final success = await controller.deleteEvent(event.businessId!);
              if (!mounted) return;
              if (success) {
                MessageUtils.showSuccess(context, 'Event deleted successfully');
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
