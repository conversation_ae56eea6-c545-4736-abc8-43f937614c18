import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';

import '../../Cattle/models/cattle_isar.dart';
import '../models/milk_record_isar.dart';
import '../services/milk_repository.dart';


/// Controller for managing milk details screen state and data
class MilkDetailsController extends ChangeNotifier {
  final MilkRepository _milkRepository = GetIt.instance<MilkRepository>();
  final Isar _isar = GetIt.instance<Isar>();

  // State management
  bool _isLoading = false;
  String? _error;
  CattleIsar? _cattle;

  // Milk data
  List<MilkRecordIsar> _milkRecords = [];

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  CattleIsar? get cattle => _cattle;
  List<MilkRecordIsar> get milkRecords => _milkRecords;

  // Computed properties for analytics (following weight module pattern)
  int get totalMilkRecords => _milkRecords.length;

  double get totalMilkProduced {
    return _milkRecords.fold(0.0, (sum, record) => sum + (record.totalAmount ?? 0.0));
  }

  double get currentMilkProduction {
    if (_milkRecords.isEmpty) return 0.0;
    final sorted = List<MilkRecordIsar>.from(_milkRecords)
      ..sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
    return sorted.first.totalAmount ?? 0.0;
  }

  double get averageMilkProduction {
    if (_milkRecords.isEmpty) return 0.0;
    return totalMilkProduced / _milkRecords.length;
  }

  double get peakMilkProduction {
    if (_milkRecords.isEmpty) return 0.0;
    return _milkRecords.map((r) => r.totalAmount ?? 0.0).reduce((a, b) => a > b ? a : b);
  }
  
  List<MilkRecordIsar> get recentRecords {
    final sorted = List<MilkRecordIsar>.from(_milkRecords)
      ..sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
    return sorted.take(10).toList();
  }

  Map<String, int> get sessionDistribution {
    final distribution = <String, int>{};
    for (final record in _milkRecords) {
      final session = record.session ?? 'Unknown';
      distribution[session] = (distribution[session] ?? 0) + 1;
    }
    return distribution;
  }

  double get dailyAverage {
    if (_milkRecords.isEmpty) return 0.0;
    
    // Group records by date
    final dailyTotals = <String, double>{};
    for (final record in _milkRecords) {
      if (record.date != null) {
        final dateKey = '${record.date!.year}-${record.date!.month}-${record.date!.day}';
        dailyTotals[dateKey] = (dailyTotals[dateKey] ?? 0.0) + (record.quantity ?? 0.0);
      }
    }
    
    if (dailyTotals.isEmpty) return 0.0;
    final totalDays = dailyTotals.length;
    final totalMilk = dailyTotals.values.fold(0.0, (sum, daily) => sum + daily);
    
    return totalMilk / totalDays;
  }

  /// Initialize controller with cattle data
  void initialize(CattleIsar cattle) {
    _cattle = cattle;
    _loadMilkData();
  }

  /// Load all milk-related data for the cattle
  Future<void> _loadMilkData() async {
    if (_cattle == null) return;

    try {
      _setLoading(true);
      _clearError();

      // Load all milk records and filter for this cattle
      final allMilkRecords = await _isar.milkRecordIsars.where().findAll();
      _milkRecords = allMilkRecords.where((record) => record.cattleBusinessId == _cattle!.businessId).toList();

      _setLoading(false);
    } catch (e) {
      _setError('Failed to load milk data: $e');
      _setLoading(false);
    }
  }

  /// Refresh all data
  Future<void> refresh() async {
    await _loadMilkData();
  }

  /// Add a new milk record
  Future<bool> addMilkRecord(MilkRecordIsar record) async {
    try {
      _setLoading(true);
      await _milkRepository.saveMilkRecord(record);
      await _loadMilkData(); // Refresh data
      return true;
    } catch (e) {
      _setError('Failed to add milk record: $e');
      _setLoading(false);
      return false;
    }
  }

  /// Update an existing milk record
  Future<bool> updateMilkRecord(MilkRecordIsar record) async {
    try {
      _setLoading(true);
      await _milkRepository.saveMilkRecord(record);
      await _loadMilkData(); // Refresh data
      return true;
    } catch (e) {
      _setError('Failed to update milk record: $e');
      _setLoading(false);
      return false;
    }
  }

  /// Delete a milk record
  Future<bool> deleteMilkRecord(int recordId) async {
    try {
      _setLoading(true);
      await _milkRepository.deleteMilkRecord(recordId);
      await _loadMilkData(); // Refresh data
      return true;
    } catch (e) {
      _setError('Failed to delete milk record: $e');
      _setLoading(false);
      return false;
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  @override
  void dispose() {
    // Clean up any resources
    super.dispose();
  }
}
